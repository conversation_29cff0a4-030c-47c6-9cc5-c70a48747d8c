# 管理员与用户登录注册API接口文档

## 概述

本文档详细说明了管理员和用户的登录、注册、密码管理等相关API接口。系统已完全支持手机号格式的账号体系，管理员和用户都使用手机号作为账号标识。

## 基础信息

- **服务地址**: `http://localhost:8080`
- **API版本**: v1
- **内容类型**: `application/json`
- **字符编码**: UTF-8

## 通用响应格式

```json
{
  "code": 200,
  "message": "success",
  "data": {}
}
```

### 状态码说明

- `200`: 成功
- `400`: 请求参数错误
- `401`: 未授权/认证失败
- `403`: 权限不足
- `404`: 资源不存在
- `500`: 服务器内部错误

## 用户相关接口

### 1. 用户注册

**接口地址**: `POST /api/v1/user/register`

**请求参数**:
```json
{
  "phone": "13800138000",
  "password": "password123",
  "code": "123456",
  "invite_code": "SOLVE2024"
}
```

**参数说明**:
- `phone`: 手机号，11位数字
- `password`: 密码，6-20位，支持大小写字母、数字、常用标点符号
- `code`: 短信验证码，6位数字
- `invite_code`: 邀请码，默认为"SOLVE2024"

**响应示例**:
```json
{
  "code": 200,
  "message": "注册成功",
  "data": {
    "id": 1,
    "phone": "13800138000",
    "balance": 0,
    "status": 1,
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T00:00:00Z"
  }
}
```

### 2. 用户登录

**接口地址**: `POST /api/v1/user/login`

**请求参数**:
```json
{
  "phone": "13800138000",
  "password": "password123"
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "登录成功",
  "data": {
    "id": 1,
    "phone": "13800138000",
    "balance": 100.50,
    "status": 1,
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T00:00:00Z"
  }
}
```

### 3. 发送注册验证码

**接口地址**: `POST /api/v1/user/send-code`

**请求参数**:
```json
{
  "phone": "13800138000"
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "验证码发送成功",
  "data": null
}
```

### 4. 用户修改密码（使用原密码）

**接口地址**: `PUT /api/v1/user/{user_id}/change-password`

**请求参数**:
```json
{
  "old_password": "oldpassword123",
  "new_password": "newpassword123"
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "密码修改成功",
  "data": {
    "id": 1,
    "phone": "13800138000",
    "balance": 100.50,
    "status": 1,
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T00:00:00Z"
  }
}
```

### 5. 发送忘记密码验证码

**接口地址**: `POST /api/v1/user/forgot-password`

**请求参数**:
```json
{
  "phone": "13800138000"
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "验证码发送成功",
  "data": null
}
```

### 6. 用户重置密码（使用验证码）

**接口地址**: `POST /api/v1/user/reset-password`

**请求参数**:
```json
{
  "phone": "13800138000",
  "code": "123456",
  "new_password": "newpassword123"
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "密码重置成功",
  "data": {
    "id": 1,
    "phone": "13800138000",
    "balance": 100.50,
    "status": 1,
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T00:00:00Z"
  }
}
```

## 管理员相关接口

### 1. 管理员登录

**接口地址**: `POST /api/v1/admin/login`

**请求参数**:
```json
{
  "phone": "15688515913",
  "password": "admin888"
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "登录成功",
  "data": {
    "id": 1,
    "phone": "15688515913",
    "role": 1,
    "role_name": "超级管理员",
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T00:00:00Z"
  }
}
```

**角色说明**:
- `1`: 超级管理员 - 拥有所有权限
- `2`: 普通管理员 - 有限权限

### 2. 管理员修改密码（使用原密码）

**接口地址**: `PUT /api/v1/admin/{admin_id}/change-password`

**请求参数**:
```json
{
  "old_password": "admin888",
  "new_password": "newadmin123"
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "密码修改成功",
  "data": {
    "id": 1,
    "phone": "15688515913",
    "role": 1,
    "role_name": "超级管理员",
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T00:00:00Z"
  }
}
```

### 3. 发送管理员忘记密码验证码

**接口地址**: `POST /api/v1/admin/forgot-password`

**请求参数**:
```json
{
  "phone": "15688515913"
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "验证码发送成功",
  "data": null
}
```

### 4. 管理员重置密码（使用验证码）

**接口地址**: `POST /api/v1/admin/reset-password`

**请求参数**:
```json
{
  "phone": "15688515913",
  "code": "123456",
  "new_password": "newadmin123"
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "密码重置成功",
  "data": {
    "id": 1,
    "phone": "15688515913",
    "role": 1,
    "role_name": "超级管理员",
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T00:00:00Z"
  }
}
```

## 权限管理

### 管理员权限说明

#### 超级管理员权限
- 用户管理：查看、管理所有用户
- 应用管理：查看、管理所有应用
- 系统配置：修改系统配置
- 管理员管理：创建、管理其他管理员
- 模型配置：配置AI模型参数
- 统计查看：查看所有统计数据
- 日志管理：查看所有系统日志

#### 普通管理员权限
- 用户管理：查看、管理用户（有限）
- 应用管理：查看、管理应用（有限）
- 统计查看：查看统计数据（有限）
- 日志管理：查看日志（有限）

### 用户权限说明
- 应用管理：只能管理自己创建的应用
- 日志查看：只能查看自己应用的相关日志
- 账户管理：管理自己的账户信息和余额

## 错误处理

### 常见错误码

#### 400 错误
```json
{
  "code": 400,
  "message": "手机号格式不正确",
  "data": null
}
```

#### 401 错误
```json
{
  "code": 401,
  "message": "密码错误",
  "data": null
}
```

#### 403 错误
```json
{
  "code": 403,
  "message": "账户已被冻结",
  "data": null
}
```

## 测试账号

### 管理员测试账号
- **手机号**: 15688515913
- **密码**: admin888
- **角色**: 超级管理员

### 短信验证码测试
- **测试手机号**: 15653259315
- **开发环境**: 使用真实短信服务
- **Redis不可用时**: 固定验证码 123456

## 注意事项

1. **手机号格式**: 必须是11位数字的中国大陆手机号
2. **密码强度**: 6-20位，支持大小写字母、数字、常用标点符号
3. **验证码**: 6位数字，有效期5分钟
4. **发送频率**: 同一手机号60秒内只能发送一次验证码
5. **账号状态**: 被冻结的账号无法登录和重置密码
6. **权限控制**: 管理员接口需要相应的权限验证

## 开发调试

### 启动服务
```bash
# 开发模式启动
./dev.sh

# 或者直接运行
go run cmd/main.go
```

### 测试接口
```bash
# 测试用户登录
curl -X POST http://localhost:8080/api/v1/user/login \
  -H "Content-Type: application/json" \
  -d '{"phone":"13800138000","password":"password123"}'

# 测试管理员登录
curl -X POST http://localhost:8080/api/v1/admin/login \
  -H "Content-Type: application/json" \
  -d '{"phone":"15688515913","password":"admin888"}'
```
