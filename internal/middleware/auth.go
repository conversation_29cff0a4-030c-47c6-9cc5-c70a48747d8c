package middleware

import (
	"solve_api/internal/database"
	"solve_api/internal/model"
	"solve_api/internal/repository"
	"solve_api/internal/utils"
	"strings"

	"github.com/gin-gonic/gin"
)

// APIAuth API密钥认证中间件
func APIAuth() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 跳过健康检查和管理员接口
		if strings.HasPrefix(c.Request.URL.Path, "/health") ||
			strings.HasPrefix(c.Request.URL.Path, "/api/v1/admin") ||
			strings.HasPrefix(c.Request.URL.Path, "/api/v1/user") {
			c.Next()
			return
		}

		// 获取认证头
		appKey := c.GetHeader("X-App-Key")
		secretKey := c.GetHeader("X-Secret-Key")

		if appKey == "" || secretKey == "" {
			utils.Unauthorized(c, "缺少API密钥")
			c.Abort()
			return
		}

		// 验证应用密钥
		appRepo := repository.NewApplicationRepository(database.GetDB())
		app, err := appRepo.GetByAppKey(appKey)
		if err != nil {
			utils.ServerError(c, "验证API密钥失败")
			c.Abort()
			return
		}

		if app == nil {
			utils.Unauthorized(c, "无效的AppKey")
			c.Abort()
			return
		}

		// 验证SecretKey
		if app.SecretKey != secretKey {
			utils.Unauthorized(c, "无效的SecretKey")
			c.Abort()
			return
		}

		// 检查应用状态
		if app.IsFrozen() {
			utils.Forbidden(c, "应用已被冻结")
			c.Abort()
			return
		}

		// 检查用户状态
		userRepo := repository.NewUserRepository(database.GetDB())
		user, err := userRepo.GetByID(app.UserID)
		if err != nil {
			utils.ServerError(c, "查询用户信息失败")
			c.Abort()
			return
		}

		if user == nil {
			utils.Unauthorized(c, "用户不存在")
			c.Abort()
			return
		}

		if user.IsFrozen() {
			utils.Forbidden(c, "用户账户已被冻结")
			c.Abort()
			return
		}

		// 将应用和用户信息存储到上下文中
		c.Set("app", app)
		c.Set("user", user)
		c.Set("app_id", app.ID)
		c.Set("user_id", user.ID)
		c.Set("app_type", app.Type)

		c.Next()
	}
}

// GetAppFromContext 从上下文获取应用信息
func GetAppFromContext(c *gin.Context) (*model.Application, bool) {
	app, exists := c.Get("app")
	if !exists {
		return nil, false
	}
	return app.(*model.Application), true
}

// GetUserFromContext 从上下文获取用户信息
func GetUserFromContext(c *gin.Context) (*model.User, bool) {
	user, exists := c.Get("user")
	if !exists {
		return nil, false
	}
	return user.(*model.User), true
}

// GetAppIDFromContext 从上下文获取应用ID
func GetAppIDFromContext(c *gin.Context) (uint, bool) {
	appID, exists := c.Get("app_id")
	if !exists {
		return 0, false
	}
	return appID.(uint), true
}

// GetUserIDFromContext 从上下文获取用户ID
func GetUserIDFromContext(c *gin.Context) (uint, bool) {
	userID, exists := c.Get("user_id")
	if !exists {
		return 0, false
	}
	return userID.(uint), true
}

// GetAppTypeFromContext 从上下文获取应用类型
func GetAppTypeFromContext(c *gin.Context) (int, bool) {
	appType, exists := c.Get("app_type")
	if !exists {
		return 0, false
	}
	return appType.(int), true
}
