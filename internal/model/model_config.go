package model

import (
	"encoding/json"
	"time"

	"gorm.io/gorm"
)

// ModelConfig 模型配置表
type ModelConfig struct {
	ID        uint           `gorm:"primaryKey" json:"id"`
	Name      string         `gorm:"uniqueIndex;size:50;not null" json:"name"`
	ApiURL    string         `gorm:"size:255;not null" json:"api_url"`
	ApiKey    string         `gorm:"size:100;not null" json:"api_key"`
	Params    string         `gorm:"type:text;comment:'JSON格式的参数配置'" json:"params"`
	Status    int            `gorm:"default:1;comment:'1:启用 2:禁用'" json:"status"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `gorm:"index" json:"-"`
}

// TableName 指定表名
func (ModelConfig) TableName() string {
	return "model_configs"
}

// ModelConfigStatus 模型配置状态常量
const (
	ModelConfigStatusEnabled  = 1 // 启用
	ModelConfigStatusDisabled = 2 // 禁用
)

// IsEnabled 检查模型是否启用
func (m *ModelConfig) IsEnabled() bool {
	return m.Status == ModelConfigStatusEnabled
}

// IsDisabled 检查模型是否禁用
func (m *ModelConfig) IsDisabled() bool {
	return m.Status == ModelConfigStatusDisabled
}

// GetParamsMap 获取参数映射
func (m *ModelConfig) GetParamsMap() (map[string]interface{}, error) {
	if m.Params == "" {
		return make(map[string]interface{}), nil
	}

	var params map[string]interface{}
	err := json.Unmarshal([]byte(m.Params), &params)
	if err != nil {
		return nil, err
	}

	return params, nil
}

// SetParamsMap 设置参数映射
func (m *ModelConfig) SetParamsMap(params map[string]interface{}) error {
	data, err := json.Marshal(params)
	if err != nil {
		return err
	}

	m.Params = string(data)
	return nil
}

// ModelConfigCreateRequest 创建模型配置请求
type ModelConfigCreateRequest struct {
	Name   string                 `json:"name" binding:"required,min=1,max=50" example:"qwen-vl-plus"`
	ApiURL string                 `json:"api_url" binding:"required,url" example:"https://api.example.com/v1/chat"`
	ApiKey string                 `json:"api_key" binding:"required" example:"sk-xxx"`
	Params map[string]interface{} `json:"params" example:"{\"temperature\":0.7,\"max_tokens\":1000}"`
	Status int                    `json:"status" binding:"omitempty,oneof=1 2" example:"1"`
}

// ModelConfigUpdateRequest 更新模型配置请求
type ModelConfigUpdateRequest struct {
	Name   string                 `json:"name" binding:"omitempty,min=1,max=50" example:"qwen-vl-plus"`
	ApiURL string                 `json:"api_url" binding:"omitempty,url" example:"https://api.example.com/v1/chat"`
	ApiKey string                 `json:"api_key" binding:"omitempty" example:"sk-xxx"`
	Params map[string]interface{} `json:"params" example:"{\"temperature\":0.7,\"max_tokens\":1000}"`
	Status int                    `json:"status" binding:"omitempty,oneof=1 2" example:"1"`
}

// ModelConfigStatusUpdateRequest 更新模型状态请求
type ModelConfigStatusUpdateRequest struct {
	Status int `json:"status" binding:"required,oneof=1 2" example:"1"`
}

// ModelConfigResponse 模型配置响应
type ModelConfigResponse struct {
	ID         uint                   `json:"id"`
	Name       string                 `json:"name"`
	ApiURL     string                 `json:"api_url"`
	ApiKey     string                 `json:"api_key,omitempty"` // 可选择是否返回
	Params     map[string]interface{} `json:"params"`
	Status     int                    `json:"status"`
	StatusName string                 `json:"status_name"`
	CreatedAt  time.Time              `json:"created_at"`
	UpdatedAt  time.Time              `json:"updated_at"`
}

// ToResponse 转换为响应格式
func (m *ModelConfig) ToResponse(includeApiKey bool) *ModelConfigResponse {
	params, _ := m.GetParamsMap()
	
	statusName := ""
	switch m.Status {
	case ModelConfigStatusEnabled:
		statusName = "启用"
	case ModelConfigStatusDisabled:
		statusName = "禁用"
	}

	response := &ModelConfigResponse{
		ID:         m.ID,
		Name:       m.Name,
		ApiURL:     m.ApiURL,
		Params:     params,
		Status:     m.Status,
		StatusName: statusName,
		CreatedAt:  m.CreatedAt,
		UpdatedAt:  m.UpdatedAt,
	}

	if includeApiKey {
		response.ApiKey = m.ApiKey
	}

	return response
}

// ModelConfigListResponse 模型配置列表响应（不包含ApiKey）
type ModelConfigListResponse struct {
	ID         uint                   `json:"id"`
	Name       string                 `json:"name"`
	ApiURL     string                 `json:"api_url"`
	Params     map[string]interface{} `json:"params"`
	Status     int                    `json:"status"`
	StatusName string                 `json:"status_name"`
	CreatedAt  time.Time              `json:"created_at"`
	UpdatedAt  time.Time              `json:"updated_at"`
}

// ToListResponse 转换为列表响应格式（不包含ApiKey）
func (m *ModelConfig) ToListResponse() *ModelConfigListResponse {
	params, _ := m.GetParamsMap()
	
	statusName := ""
	switch m.Status {
	case ModelConfigStatusEnabled:
		statusName = "启用"
	case ModelConfigStatusDisabled:
		statusName = "禁用"
	}

	return &ModelConfigListResponse{
		ID:         m.ID,
		Name:       m.Name,
		ApiURL:     m.ApiURL,
		Params:     params,
		Status:     m.Status,
		StatusName: statusName,
		CreatedAt:  m.CreatedAt,
		UpdatedAt:  m.UpdatedAt,
	}
}

// 预定义的模型名称常量
const (
	ModelNameQwenVLPlus   = "qwen-vl-plus"
	ModelNameDeepseekChat = "deepseek-chat"
)

// GetDefaultModelParams 获取默认模型参数
func GetDefaultModelParams(modelName string) map[string]interface{} {
	switch modelName {
	case ModelNameQwenVLPlus:
		return map[string]interface{}{
			"temperature": 0.7,
			"max_tokens":  1000,
			"top_p":       0.9,
		}
	case ModelNameDeepseekChat:
		return map[string]interface{}{
			"temperature": 0.7,
			"max_tokens":  2000,
			"top_p":       0.9,
		}
	default:
		return map[string]interface{}{
			"temperature": 0.7,
			"max_tokens":  1000,
		}
	}
}
