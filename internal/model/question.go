package model

import (
	"crypto/md5"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"time"

	"gorm.io/gorm"
)

// Question 题目表
type Question struct {
	ID          uint           `gorm:"primaryKey" json:"id"`
	Hash        string         `gorm:"uniqueIndex;size:64;not null;comment:'题目哈希值'" json:"hash"`
	Content     string         `gorm:"type:text;not null;comment:'题目内容'" json:"content"`
	Analysis    string         `gorm:"type:text;comment:'题目解析'" json:"analysis"`
	Answer      string         `gorm:"type:text;comment:'题目答案'" json:"answer"`
	Subject     string         `gorm:"size:20;comment:'学科'" json:"subject"`
	Grade       string         `gorm:"size:20;comment:'年级'" json:"grade"`
	Difficulty  int            `gorm:"comment:'难度 1-5'" json:"difficulty"`
	SourceModel string         `gorm:"size:50;comment:'来源模型'" json:"source_model"`
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `gorm:"index" json:"-"`
}

// TableName 指定表名
func (Question) TableName() string {
	return "questions"
}

// QuestionDifficulty 题目难度常量
const (
	QuestionDifficultyVeryEasy = 1 // 非常简单
	QuestionDifficultyEasy     = 2 // 简单
	QuestionDifficultyMedium   = 3 // 中等
	QuestionDifficultyHard     = 4 // 困难
	QuestionDifficultyVeryHard = 5 // 非常困难
)

// GenerateHash 生成题目哈希值
func (q *Question) GenerateHash() {
	// 使用题目内容生成MD5哈希
	hasher := md5.New()
	hasher.Write([]byte(q.Content))
	q.Hash = hex.EncodeToString(hasher.Sum(nil))
}

// QuestionSearchRequest 拍照搜题请求
type QuestionSearchRequest struct {
	ImageURL string `json:"image_url" binding:"required,url" example:"https://example.com/image.jpg"`
}

// QuestionSearchResponse 拍照搜题响应
type QuestionSearchResponse struct {
	ID          uint   `json:"id"`
	Content     string `json:"content"`
	Analysis    string `json:"analysis"`
	Answer      string `json:"answer"`
	Subject     string `json:"subject"`
	Grade       string `json:"grade"`
	Difficulty  int    `json:"difficulty"`
	SourceModel string `json:"source_model"`
	CacheHit    bool   `json:"cache_hit"`    // 是否命中缓存
	ProcessTime int64  `json:"process_time"` // 处理时间（毫秒）
}

// ToSearchResponse 转换为搜索响应格式
func (q *Question) ToSearchResponse(cacheHit bool, processTime int64) *QuestionSearchResponse {
	return &QuestionSearchResponse{
		ID:          q.ID,
		Content:     q.Content,
		Analysis:    q.Analysis,
		Answer:      q.Answer,
		Subject:     q.Subject,
		Grade:       q.Grade,
		Difficulty:  q.Difficulty,
		SourceModel: q.SourceModel,
		CacheHit:    cacheHit,
		ProcessTime: processTime,
	}
}

// QuestionResponse 题目响应
type QuestionResponse struct {
	ID          uint      `json:"id"`
	Hash        string    `json:"hash"`
	Content     string    `json:"content"`
	Analysis    string    `json:"analysis"`
	Answer      string    `json:"answer"`
	Subject     string    `json:"subject"`
	Grade       string    `json:"grade"`
	Difficulty  int       `json:"difficulty"`
	SourceModel string    `json:"source_model"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

// ToResponse 转换为响应格式
func (q *Question) ToResponse() *QuestionResponse {
	return &QuestionResponse{
		ID:          q.ID,
		Hash:        q.Hash,
		Content:     q.Content,
		Analysis:    q.Analysis,
		Answer:      q.Answer,
		Subject:     q.Subject,
		Grade:       q.Grade,
		Difficulty:  q.Difficulty,
		SourceModel: q.SourceModel,
		CreatedAt:   q.CreatedAt,
		UpdatedAt:   q.UpdatedAt,
	}
}

// QwenVLResponse Qwen-VL模型响应结构
type QwenVLResponse struct {
	Output struct {
		Choices []struct {
			Message struct {
				Content string `json:"content"`
			} `json:"message"`
		} `json:"choices"`
	} `json:"output"`
	Usage struct {
		InputTokens  int `json:"input_tokens"`
		OutputTokens int `json:"output_tokens"`
		TotalTokens  int `json:"total_tokens"`
	} `json:"usage"`
}

// DeepseekResponse Deepseek模型响应结构
type DeepseekResponse struct {
	Choices []struct {
		Message struct {
			Content string `json:"content"`
		} `json:"message"`
	} `json:"choices"`
	Usage struct {
		PromptTokens     int `json:"prompt_tokens"`
		CompletionTokens int `json:"completion_tokens"`
		TotalTokens      int `json:"total_tokens"`
	} `json:"usage"`
}

// QuestionStructure 题目结构化数据
type QuestionStructure struct {
	Content    string `json:"content"`
	Subject    string `json:"subject"`
	Grade      string `json:"grade"`
	Difficulty int    `json:"difficulty"`
}

// GenerateHashFromStructure 从结构化数据生成哈希
func GenerateHashFromStructure(structure *QuestionStructure) string {
	// 将结构化数据序列化为JSON字符串
	data, _ := json.Marshal(structure)
	
	// 生成MD5哈希
	hasher := md5.New()
	hasher.Write(data)
	return hex.EncodeToString(hasher.Sum(nil))
}

// GetDifficultyName 获取难度名称
func GetDifficultyName(difficulty int) string {
	switch difficulty {
	case QuestionDifficultyVeryEasy:
		return "非常简单"
	case QuestionDifficultyEasy:
		return "简单"
	case QuestionDifficultyMedium:
		return "中等"
	case QuestionDifficultyHard:
		return "困难"
	case QuestionDifficultyVeryHard:
		return "非常困难"
	default:
		return "未知"
	}
}

// ValidateImageURL 验证图片URL
func ValidateImageURL(url string) error {
	if url == "" {
		return fmt.Errorf("图片URL不能为空")
	}
	
	// 这里可以添加更多的URL验证逻辑
	// 比如检查URL格式、文件扩展名等
	
	return nil
}

// CacheKey 生成缓存键
func GenerateCacheKey(hash string) string {
	return fmt.Sprintf("question:%s", hash)
}
