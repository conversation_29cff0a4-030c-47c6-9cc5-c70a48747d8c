package service

import (
	"fmt"
	"solve_api/internal/model"
	"solve_api/internal/repository"
	"time"
)

type QuestionService struct {
	questionRepo      *repository.QuestionRepository
	questionCacheRepo *repository.QuestionCacheRepository
	aiService         *AIService
	userRepo          *repository.UserRepository
	balanceLogRepo    *repository.BalanceLogRepository
}

// NewQuestionService 创建题目服务实例
func NewQuestionService(
	questionRepo *repository.QuestionRepository,
	questionCacheRepo *repository.QuestionCacheRepository,
	aiService *AIService,
	userRepo *repository.UserRepository,
	balanceLogRepo *repository.BalanceLogRepository,
) *QuestionService {
	return &QuestionService{
		questionRepo:      questionRepo,
		questionCacheRepo: questionCacheRepo,
		aiService:         aiService,
		userRepo:          userRepo,
		balanceLogRepo:    balanceLogRepo,
	}
}

// Search 拍照搜题主要业务逻辑
func (s *QuestionService) Search(userID uint, req *model.QuestionSearchRequest) (*model.QuestionSearchResponse, error) {
	startTime := time.Now()

	// 1. 验证图片URL
	if err := s.aiService.ValidateImageURL(req.ImageURL); err != nil {
		return nil, fmt.Errorf("图片URL验证失败: %w", err)
	}

	// 2. 调用Qwen-VL获取题目结构
	structure, err := s.aiService.CallQwenVL(req.ImageURL)
	if err != nil {
		return nil, fmt.Errorf("图像识别失败: %w", err)
	}

	// 3. 生成题目哈希
	hash := model.GenerateHashFromStructure(structure)

	// 4. 查询Redis缓存
	cachedQuestion, err := s.questionCacheRepo.Get(hash)
	if err == nil && cachedQuestion != nil {
		processTime := time.Since(startTime).Milliseconds()
		return cachedQuestion.ToSearchResponse(true, processTime), nil
	}

	// 5. 查询MySQL数据库
	dbQuestion, err := s.questionRepo.GetByHash(hash)
	if err != nil {
		return nil, fmt.Errorf("查询数据库失败: %w", err)
	}

	var question *model.Question
	cacheHit := false

	if dbQuestion != nil {
		// 数据库中存在，更新缓存
		question = dbQuestion
		s.questionCacheRepo.Set(hash, question) // 异步更新缓存，不处理错误
	} else {
		// 6. 调用Deepseek进行题目解析
		analysis, answer, err := s.aiService.CallDeepseek(structure.Content)
		if err != nil {
			return nil, fmt.Errorf("题目解析失败: %w", err)
		}

		// 7. 创建新题目记录
		question = &model.Question{
			Content:     structure.Content,
			Analysis:    analysis,
			Answer:      answer,
			Subject:     structure.Subject,
			Grade:       structure.Grade,
			Difficulty:  structure.Difficulty,
			SourceModel: "qwen-vl-plus,deepseek-chat",
		}

		// 生成哈希
		question.GenerateHash()

		// 8. 保存到数据库
		if err := s.questionRepo.Create(question); err != nil {
			return nil, fmt.Errorf("保存题目失败: %w", err)
		}

		// 9. 保存到缓存
		s.questionCacheRepo.Set(hash, question) // 异步更新缓存，不处理错误
	}

	// 10. 扣费处理
	if err := s.processPayment(userID, model.ApplicationTypePhotoSearch); err != nil {
		// 扣费失败不影响返回结果，但记录日志
		// 这里可以添加日志记录
	}

	processTime := time.Since(startTime).Milliseconds()
	return question.ToSearchResponse(cacheHit, processTime), nil
}

// processPayment 处理扣费
func (s *QuestionService) processPayment(userID uint, serviceType int) error {
	// 获取用户信息
	user, err := s.userRepo.GetByID(userID)
	if err != nil {
		return fmt.Errorf("查询用户信息失败: %w", err)
	}
	if user == nil {
		return fmt.Errorf("用户不存在")
	}

	// 获取服务价格（这里简化处理，使用固定价格）
	price := 0.01 // 默认0.01元/次

	// 检查余额
	if user.Balance < price {
		return fmt.Errorf("余额不足")
	}

	// 扣费
	user.Balance -= price
	if err := s.userRepo.Update(user); err != nil {
		return fmt.Errorf("扣费失败: %w", err)
	}

	// 记录余额变动日志
	balanceLog := &model.BalanceLog{
		UserID:      userID,
		Amount:      -price,
		Balance:     user.Balance,
		Type:        model.BalanceLogTypeConsume,
		Description: "拍照搜题服务费用",
		RelatedID:   0, // 可以关联题目ID
	}

	if err := s.balanceLogRepo.Create(balanceLog); err != nil {
		// 日志记录失败不影响主流程
		return nil
	}

	return nil
}

// GetByID 根据ID获取题目
func (s *QuestionService) GetByID(id uint) (*model.QuestionResponse, error) {
	question, err := s.questionRepo.GetByID(id)
	if err != nil {
		return nil, fmt.Errorf("查询题目失败: %w", err)
	}
	if question == nil {
		return nil, fmt.Errorf("题目不存在")
	}

	return question.ToResponse(), nil
}

// GetList 获取题目列表
func (s *QuestionService) GetList(page, pageSize int) ([]*model.QuestionResponse, int64, error) {
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 10
	}

	offset := (page - 1) * pageSize
	questions, total, err := s.questionRepo.List(offset, pageSize)
	if err != nil {
		return nil, 0, fmt.Errorf("查询题目列表失败: %w", err)
	}

	var result []*model.QuestionResponse
	for _, question := range questions {
		result = append(result, question.ToResponse())
	}

	return result, total, nil
}

// GetBySubject 根据学科获取题目列表
func (s *QuestionService) GetBySubject(subject string, page, pageSize int) ([]*model.QuestionResponse, int64, error) {
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 10
	}

	offset := (page - 1) * pageSize
	questions, total, err := s.questionRepo.GetBySubject(subject, offset, pageSize)
	if err != nil {
		return nil, 0, fmt.Errorf("查询题目列表失败: %w", err)
	}

	var result []*model.QuestionResponse
	for _, question := range questions {
		result = append(result, question.ToResponse())
	}

	return result, total, nil
}

// Search 搜索题目
func (s *QuestionService) SearchQuestions(keyword string, page, pageSize int) ([]*model.QuestionResponse, int64, error) {
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 10
	}

	offset := (page - 1) * pageSize
	questions, total, err := s.questionRepo.Search(keyword, offset, pageSize)
	if err != nil {
		return nil, 0, fmt.Errorf("搜索题目失败: %w", err)
	}

	var result []*model.QuestionResponse
	for _, question := range questions {
		result = append(result, question.ToResponse())
	}

	return result, total, nil
}

// GetStats 获取题目统计信息
func (s *QuestionService) GetStats() (map[string]interface{}, error) {
	// 获取总数
	totalCount, err := s.questionRepo.GetTotalCount()
	if err != nil {
		return nil, fmt.Errorf("获取题目总数失败: %w", err)
	}

	// 获取各学科统计
	subjectCount, err := s.questionRepo.GetCountBySubject()
	if err != nil {
		return nil, fmt.Errorf("获取学科统计失败: %w", err)
	}

	// 获取各年级统计
	gradeCount, err := s.questionRepo.GetCountByGrade()
	if err != nil {
		return nil, fmt.Errorf("获取年级统计失败: %w", err)
	}

	// 获取各难度统计
	difficultyCount, err := s.questionRepo.GetCountByDifficulty()
	if err != nil {
		return nil, fmt.Errorf("获取难度统计失败: %w", err)
	}

	// 获取缓存统计
	cacheStats, err := s.questionCacheRepo.GetCacheStats()
	if err != nil {
		cacheStats = map[string]interface{}{
			"redis_available": false,
			"total_keys":      0,
		}
	}

	return map[string]interface{}{
		"total_questions":   totalCount,
		"subject_count":     subjectCount,
		"grade_count":       gradeCount,
		"difficulty_count":  difficultyCount,
		"cache_stats":       cacheStats,
	}, nil
}

// ClearCache 清空题目缓存
func (s *QuestionService) ClearCache() error {
	return s.questionCacheRepo.Clear()
}
