package service

import (
	"fmt"
	"solve_api/internal/model"
	"solve_api/internal/repository"
	"time"
)

type APILogService struct {
	apiLogRepo *repository.APILogRepository
}

// NewAPILogService 创建API日志服务实例
func NewAPILogService(apiLogRepo *repository.APILogRepository) *APILogService {
	return &APILogService{
		apiLogRepo: apiLogRepo,
	}
}

// CreateAPILog 创建API日志
func (s *APILogService) CreateAPILog(log *model.APILog) error {
	return s.apiLogRepo.Create(log)
}

// GetAPILog 获取API日志详情
func (s *APILogService) GetAPILog(id uint) (*model.APILogResponse, error) {
	log, err := s.apiLogRepo.GetByID(id)
	if err != nil {
		return nil, fmt.Errorf("查询API日志失败: %w", err)
	}
	if log == nil {
		return nil, fmt.Errorf("API日志不存在")
	}

	return log.ToResponse(), nil
}

// GetAPILogs 获取API日志列表
func (s *APILogService) GetAPILogs(page, pageSize int) ([]*model.APILogResponse, int64, error) {
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 10
	}

	offset := (page - 1) * pageSize
	logs, total, err := s.apiLogRepo.List(offset, pageSize)
	if err != nil {
		return nil, 0, fmt.Errorf("查询API日志列表失败: %w", err)
	}

	var result []*model.APILogResponse
	for _, log := range logs {
		result = append(result, log.ToResponse())
	}

	return result, total, nil
}

// GetUserAPILogs 获取用户API日志列表
func (s *APILogService) GetUserAPILogs(userID uint, page, pageSize int) ([]*model.APILogResponse, int64, error) {
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 10
	}

	offset := (page - 1) * pageSize
	logs, total, err := s.apiLogRepo.GetByUserID(userID, offset, pageSize)
	if err != nil {
		return nil, 0, fmt.Errorf("查询用户API日志失败: %w", err)
	}

	var result []*model.APILogResponse
	for _, log := range logs {
		result = append(result, log.ToResponse())
	}

	return result, total, nil
}

// GetAppAPILogs 获取应用API日志列表
func (s *APILogService) GetAppAPILogs(appID uint, page, pageSize int) ([]*model.APILogResponse, int64, error) {
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 10
	}

	offset := (page - 1) * pageSize
	logs, total, err := s.apiLogRepo.GetByAppID(appID, offset, pageSize)
	if err != nil {
		return nil, 0, fmt.Errorf("查询应用API日志失败: %w", err)
	}

	var result []*model.APILogResponse
	for _, log := range logs {
		result = append(result, log.ToResponse())
	}

	return result, total, nil
}

// GetAPILogsByDateRange 根据日期范围获取API日志
func (s *APILogService) GetAPILogsByDateRange(startDate, endDate time.Time, page, pageSize int) ([]*model.APILogResponse, int64, error) {
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 10
	}

	offset := (page - 1) * pageSize
	logs, total, err := s.apiLogRepo.GetByDateRange(startDate, endDate, offset, pageSize)
	if err != nil {
		return nil, 0, fmt.Errorf("查询日期范围API日志失败: %w", err)
	}

	var result []*model.APILogResponse
	for _, log := range logs {
		result = append(result, log.ToResponse())
	}

	return result, total, nil
}

// GetUserAPIStats 获取用户API统计
func (s *APILogService) GetUserAPIStats(userID uint) (map[string]interface{}, error) {
	// 获取总调用次数
	totalCalls, err := s.apiLogRepo.GetTotalCallsByUser(userID)
	if err != nil {
		return nil, fmt.Errorf("获取用户总调用次数失败: %w", err)
	}

	// 获取成功调用次数
	successCalls, err := s.apiLogRepo.GetSuccessCallsByUser(userID)
	if err != nil {
		return nil, fmt.Errorf("获取用户成功调用次数失败: %w", err)
	}

	// 获取错误调用次数
	errorCalls, err := s.apiLogRepo.GetErrorCallsByUser(userID)
	if err != nil {
		return nil, fmt.Errorf("获取用户错误调用次数失败: %w", err)
	}

	// 获取总费用
	totalCost, err := s.apiLogRepo.GetTotalCostByUser(userID)
	if err != nil {
		return nil, fmt.Errorf("获取用户总费用失败: %w", err)
	}

	// 获取平均响应时间
	avgResponseTime, err := s.apiLogRepo.GetAvgResponseTimeByUser(userID)
	if err != nil {
		return nil, fmt.Errorf("获取用户平均响应时间失败: %w", err)
	}

	// 计算成功率
	successRate := float64(0)
	if totalCalls > 0 {
		successRate = float64(successCalls) / float64(totalCalls) * 100
	}

	return map[string]interface{}{
		"total_calls":       totalCalls,
		"success_calls":     successCalls,
		"error_calls":       errorCalls,
		"total_cost":        totalCost,
		"avg_response_time": avgResponseTime,
		"success_rate":      successRate,
	}, nil
}

// GetAppAPIStats 获取应用API统计
func (s *APILogService) GetAppAPIStats(appID uint) (map[string]interface{}, error) {
	// 获取总调用次数
	totalCalls, err := s.apiLogRepo.GetTotalCallsByApp(appID)
	if err != nil {
		return nil, fmt.Errorf("获取应用总调用次数失败: %w", err)
	}

	return map[string]interface{}{
		"total_calls": totalCalls,
	}, nil
}

// CleanOldLogs 清理旧日志
func (s *APILogService) CleanOldLogs(days int) error {
	if days <= 0 {
		days = 30 // 默认保留30天
	}

	return s.apiLogRepo.DeleteOldLogs(days)
}
