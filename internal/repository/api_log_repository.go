package repository

import (
	"solve_api/internal/model"
	"time"

	"gorm.io/gorm"
)

type APILogRepository struct {
	db *gorm.DB
}

// NewAPILogRepository 创建API日志仓库实例
func NewAPILogRepository(db *gorm.DB) *APILogRepository {
	return &APILogRepository{db: db}
}

// Create 创建API日志
func (r *APILogRepository) Create(log *model.APILog) error {
	return r.db.Create(log).Error
}

// GetByID 根据ID获取API日志
func (r *APILogRepository) GetByID(id uint) (*model.APILog, error) {
	var log model.APILog
	err := r.db.First(&log, id).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return &log, nil
}

// List 获取API日志列表
func (r *APILogRepository) List(offset, limit int) ([]*model.APILog, int64, error) {
	var logs []*model.APILog
	var total int64

	// 获取总数
	if err := r.db.Model(&model.APILog{}).Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 获取分页数据
	err := r.db.Offset(offset).Limit(limit).Order("created_at DESC").Find(&logs).Error
	if err != nil {
		return nil, 0, err
	}

	return logs, total, nil
}

// GetByUserID 根据用户ID获取API日志列表
func (r *APILogRepository) GetByUserID(userID uint, offset, limit int) ([]*model.APILog, int64, error) {
	var logs []*model.APILog
	var total int64

	// 获取总数
	if err := r.db.Model(&model.APILog{}).Where("user_id = ?", userID).Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 获取分页数据
	err := r.db.Where("user_id = ?", userID).
		Offset(offset).
		Limit(limit).
		Order("created_at DESC").
		Find(&logs).Error
	if err != nil {
		return nil, 0, err
	}

	return logs, total, nil
}

// GetByAppID 根据应用ID获取API日志列表
func (r *APILogRepository) GetByAppID(appID uint, offset, limit int) ([]*model.APILog, int64, error) {
	var logs []*model.APILog
	var total int64

	// 获取总数
	if err := r.db.Model(&model.APILog{}).Where("app_id = ?", appID).Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 获取分页数据
	err := r.db.Where("app_id = ?", appID).
		Offset(offset).
		Limit(limit).
		Order("created_at DESC").
		Find(&logs).Error
	if err != nil {
		return nil, 0, err
	}

	return logs, total, nil
}

// GetByDateRange 根据日期范围获取API日志
func (r *APILogRepository) GetByDateRange(startDate, endDate time.Time, offset, limit int) ([]*model.APILog, int64, error) {
	var logs []*model.APILog
	var total int64

	// 获取总数
	if err := r.db.Model(&model.APILog{}).
		Where("created_at >= ? AND created_at <= ?", startDate, endDate).
		Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 获取分页数据
	err := r.db.Where("created_at >= ? AND created_at <= ?", startDate, endDate).
		Offset(offset).
		Limit(limit).
		Order("created_at DESC").
		Find(&logs).Error
	if err != nil {
		return nil, 0, err
	}

	return logs, total, nil
}

// GetTotalCallsByUser 获取用户总调用次数
func (r *APILogRepository) GetTotalCallsByUser(userID uint) (int64, error) {
	var count int64
	err := r.db.Model(&model.APILog{}).Where("user_id = ?", userID).Count(&count).Error
	return count, err
}

// GetTotalCallsByApp 获取应用总调用次数
func (r *APILogRepository) GetTotalCallsByApp(appID uint) (int64, error) {
	var count int64
	err := r.db.Model(&model.APILog{}).Where("app_id = ?", appID).Count(&count).Error
	return count, err
}

// GetSuccessCallsByUser 获取用户成功调用次数
func (r *APILogRepository) GetSuccessCallsByUser(userID uint) (int64, error) {
	var count int64
	err := r.db.Model(&model.APILog{}).
		Where("user_id = ? AND status_code >= 200 AND status_code < 300", userID).
		Count(&count).Error
	return count, err
}

// GetErrorCallsByUser 获取用户错误调用次数
func (r *APILogRepository) GetErrorCallsByUser(userID uint) (int64, error) {
	var count int64
	err := r.db.Model(&model.APILog{}).
		Where("user_id = ? AND (status_code < 200 OR status_code >= 300)", userID).
		Count(&count).Error
	return count, err
}

// GetTotalCostByUser 获取用户总费用
func (r *APILogRepository) GetTotalCostByUser(userID uint) (float64, error) {
	var total float64
	err := r.db.Model(&model.APILog{}).
		Where("user_id = ?", userID).
		Select("COALESCE(SUM(cost), 0)").
		Scan(&total).Error
	return total, err
}

// GetAvgResponseTimeByUser 获取用户平均响应时间
func (r *APILogRepository) GetAvgResponseTimeByUser(userID uint) (float64, error) {
	var avg float64
	err := r.db.Model(&model.APILog{}).
		Where("user_id = ?", userID).
		Select("COALESCE(AVG(response_time), 0)").
		Scan(&avg).Error
	return avg, err
}

// GetCallsByDate 获取指定日期的调用统计
func (r *APILogRepository) GetCallsByDate(date string) (int64, int64, int64, error) {
	startTime, _ := time.Parse("2006-01-02", date)
	endTime := startTime.Add(24 * time.Hour)

	var total, success, error int64

	// 总调用次数
	r.db.Model(&model.APILog{}).
		Where("created_at >= ? AND created_at < ?", startTime, endTime).
		Count(&total)

	// 成功调用次数
	r.db.Model(&model.APILog{}).
		Where("created_at >= ? AND created_at < ? AND status_code >= 200 AND status_code < 300", startTime, endTime).
		Count(&success)

	// 错误调用次数
	error = total - success

	return total, success, error, nil
}

// GetRevenueByDate 获取指定日期的收入
func (r *APILogRepository) GetRevenueByDate(date string) (float64, error) {
	startTime, _ := time.Parse("2006-01-02", date)
	endTime := startTime.Add(24 * time.Hour)

	var revenue float64
	err := r.db.Model(&model.APILog{}).
		Where("created_at >= ? AND created_at < ?", startTime, endTime).
		Select("COALESCE(SUM(cost), 0)").
		Scan(&revenue).Error

	return revenue, err
}

// GetAvgResponseTimeByDate 获取指定日期的平均响应时间
func (r *APILogRepository) GetAvgResponseTimeByDate(date string) (float64, error) {
	startTime, _ := time.Parse("2006-01-02", date)
	endTime := startTime.Add(24 * time.Hour)

	var avg float64
	err := r.db.Model(&model.APILog{}).
		Where("created_at >= ? AND created_at < ?", startTime, endTime).
		Select("COALESCE(AVG(response_time), 0)").
		Scan(&avg).Error

	return avg, err
}

// GetUserCallsByDate 获取指定用户和日期的调用统计
func (r *APILogRepository) GetUserCallsByDate(userID uint, date string) (int64, int64, int64, float64, float64, error) {
	startTime, _ := time.Parse("2006-01-02", date)
	endTime := startTime.Add(24 * time.Hour)

	var total, success, errorCount int64
	var totalCost, avgResponseTime float64

	// 总调用次数
	r.db.Model(&model.APILog{}).
		Where("user_id = ? AND created_at >= ? AND created_at < ?", userID, startTime, endTime).
		Count(&total)

	// 成功调用次数
	r.db.Model(&model.APILog{}).
		Where("user_id = ? AND created_at >= ? AND created_at < ? AND status_code >= 200 AND status_code < 300", userID, startTime, endTime).
		Count(&success)

	// 错误调用次数
	errorCount = total - success

	// 总费用
	r.db.Model(&model.APILog{}).
		Where("user_id = ? AND created_at >= ? AND created_at < ?", userID, startTime, endTime).
		Select("COALESCE(SUM(cost), 0)").
		Scan(&totalCost)

	// 平均响应时间
	r.db.Model(&model.APILog{}).
		Where("user_id = ? AND created_at >= ? AND created_at < ?", userID, startTime, endTime).
		Select("COALESCE(AVG(response_time), 0)").
		Scan(&avgResponseTime)

	return total, success, errorCount, totalCost, avgResponseTime, nil
}

// DeleteOldLogs 删除旧日志（保留指定天数）
func (r *APILogRepository) DeleteOldLogs(days int) error {
	cutoffTime := time.Now().AddDate(0, 0, -days)
	return r.db.Where("created_at < ?", cutoffTime).Delete(&model.APILog{}).Error
}
