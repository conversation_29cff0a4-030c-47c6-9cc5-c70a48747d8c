让我们来确认拍照搜题的核心功能是否已经全部就绪吧！然后查看与下述是否冲突或不足。

1.用户携带以下参数请求拍照搜题API。
App Key
Secret Key
图片url

2.鉴权用户，确认余额。满足条件则执行业务流程

3.将用户提交的图片url提交Qwen

4.将将Qwen返回的信息进行结构化解析，

5将Qwen返回的信息制作缓存键

6，查询Redis缓存，如命中则返回结果

7. 查询mysql，命中则返回结果

8. 如未命中，则调用Deepseek进行题目解析

9. 将deepseek返回的信息进行格式化解析

10.将解析的题目保存到数据库，并返回给用户

11. 返回给redis。

6.扣费并记录日志



请求qwen的参数包含
model : qwen-vl-plus
role : system",
content : ” “
role":"user",
content":[{"image":"http:\/\/solve.igmdns.com\/img\/23.jpg"},{"text":"question_text内的值不应该出现题目类型以及问题序号。"}]}]},
"parameters":{"
temperature":0,
"top_p":0.01,
"top_k":1,"
do_sample":false,
"response_format":{"type":"json_object"},
"detail":"high","
frequency_penalty":-2,
"presence_penalty":-2}}

要求这些配置的项的值存储在mysql，方便调整。deepseek同样参考这些约束控制的值。

将qwen返回的数据进行格式化解析。将返回的json进行格式化解析，以下是样本。

{
  "question_type": "多选题",
  "question_text": "(多选题)12、唐某驾驶一辆大客车，乘载74人（核载55人），以每小时38公里的速度，行至一连续下陡坡转弯路段时，机动车翻入路侧溪水内，造成17人死亡、57人受伤。唐某的主要违法行为是什么？",
  "options": {
    "A": "超速行驶",
    "B": "客车超员",
    "C": "酒后驾驶",
    "D": "疲劳驾驶"
  }
}

将解析